var oOutput = Context.createOutputObject();
var currentDB = ArisData.getActiveDatabase();
var ArabicLocale = 1025; // Arabic
var EnglishLocale = 1033; // English
var currentLocale = ArabicLocale; // Context.getSelectedLanguage()
var ID_DEFAULT_FONT = "Cambria";
var selectedModel = ArisData.getSelectedModels()[0];
var selectedObjects = selectedModel.ObjDefList();
var selectedModelName = selectedModel.Name(EnglishLocale);
var selectedGroupPath = selectedModel.Group().Path(1033);
// title width and height
var Title_Width = 80;
var Title_Height = 50;

// var selectedobjdef= GetOrCreateObjDef ( 128, 2, "x. Testing", 1033 );

//coverPage
var Main_title_font_size = 36;
var sec_title_font_size = 26;
var thr_title_font_size = 18;

//content_page
var content_page_title_size = 16;

//bodyfont
var page_title_font_size = 12;
var description_font_size = 10;
var sub_title_font_size = 11;

// table font
var table_font_size = 10;
//
var table_body = 10;
var content_Table_size = 14;
var title_font_size = 14;
var toc_size = 10;
var notation_font_size = 7;
var content_footer = 10;
var content_font_size = 10;
var content_font_size_2 = 12; // this is for Page 9

// Colors
var black = [0, 0, 0];
var white = [255, 255, 255];
var blue = [7, 85, 117];
var lightblue = [47, 84, 150];
var lightGray = [191, 191, 191];
var green = [0, 176, 80];
var red = [255, 0, 0];
var d_org = [225, 170, 140];

var headerImg = Context.createPicture("Untitled.jpg");

var tablesDescriptions = {
	processManual: "This section contains all processes that are being performed by the departments along with actor’s roles within each process.",
	processCard: "The below card defines our process boundaries that enable us from understanding & distinguishing it from other processes within the organization.",
	processNarrations: "This section contains workflow narrations/ steps, for the process flow diagram above, with details related to each activity performed.",
	raciMatrix: "This section listed the main activities performed within the process classified as per RACI Matrix standards.",
	slaTime: "This section highlights the TAT boundaries to be followed while performing the process between different actors within the process.",
};

var Filter = currentDB.ActiveFilter();
Filter.setMethodLocale(EnglishLocale);
oOutput.setLocale(EnglishLocale);

function main() {
	page1();
	Page_1_TOC();
	page2();
	page3();
	page4();
	page5();
	page6();
	runReport(oOutput);
}

function page1() {
	PAGE_NEW(oOutput, true);

	// Begin header section
	oOutput.BeginHeader();
	oOutput.OutGraphicAbsolute(headerImg, 0.0, 0.0, 278, 27, false);
	oOutput.EndHeader();

	// Insert some empty lines
	emptyLine(oOutput);
	emptyLine(oOutput);

	oOutput.BeginParagraph(Constants.FMT_CENTER, 0, 0, 0, 0, 0);
	var image = Context.createPicture("Untitled2.jpg");
	oOutput.OutGraphic(image, -1, 67, 59);
	oOutput.EndParagraph();

	emptyLine(oOutput);
	TextLine("Process Manual", sec_title_font_size, "center-bold", blue);
	emptyLine(oOutput);

	TextLine("for", thr_title_font_size, "center", blue);
	emptyLine(oOutput);

	TextLine(selectedModelName, thr_title_font_size, "center", blue);
	emptyLine(oOutput);

	//var selectedModelGroup = selectedModel.Group().Name(EnglishLocale);
	var functionName = getSecondToLastSection(selectedGroupPath);
	TextLine(functionName, thr_title_font_size, "center", blue);

	emptyLine(oOutput);
	emptyLine(oOutput);
	emptyLine(oOutput);

	createCoverPageFooter(oOutput);

	function createCoverPageFooter(outputObj) {
		outputObj.BeginFooter();
		TextLine("Head Office: P.O. Box: 31616, Al Khobar 31952, Saudi Arabia \n" + "Tel: 800 1199 222 * Fax: ************ * Email: <EMAIL> * Website: www.walaa.com \n" + "Licensed and Supervised by Insurance Authority (IA)* License #: TMN/16/20087 * UNN: **********", content_footer, "center", black);

		outputObj.EndFooter();
	}

	PAGE_END(oOutput);
}
function Page_1_TOC() {
	PAGE_NEW(oOutput, true);

	emptyLine();
	sectionPageSetup(oOutput, true);
	TextLine("Table of Content", content_page_title_size, "left", lightblue);

	emptyLine();
	oOutput.OutputField(Constants.FIELD_TOC, ID_DEFAULT_FONT, toc_size, RGB(black), Constants.C_TRANSPARENT, Constants.FMT_LEFT);
	ArisData.getActiveDatabase().clearCaches();
	PAGE_END(oOutput);
}

function page2() {
	// Process card
	var processCardAttrbuites = {
		//process_code: getAttributeValue_fromModel_ByTypeNumber(1903845, selectedModel, EnglishLocale),
		//process_name: selectedModelName,
		//process_name: getAttributeValue_fromModel_ByTypeNumber(1000, selectedModel, EnglishLocale),
		//process_description: getAttributeValue_fromModel_ByTypeNumber(2690277, selectedModel, EnglishLocale),
		//process_actors: selectedModel.ObjDefListBySymbols([Constants.ST_ORGUNIT_TYPE_LANE, Constants.ST_ORGUNIT_LANE, Constants.ST_POSITION_LANE, Constants.ST_ROLE_LANE  ]).map(function (obj) {return obj.Name(1033);}).join() ,
		//process_trigger: getAttributeValue_fromModel_ByTypeNumber(1641701, selectedModel, EnglishLocale),
		//process_endpoint: getAttributeValue_fromModel_ByTypeNumber(2755813, selectedModel, EnglishLocale),
		//system_used: getAttributeValue_fromModel_ByTypeNumber(724197, selectedModel, EnglishLocale),
		//internal_intersection: getAttributeValue_fromModel_ByTypeNumber(1445093, selectedModel, EnglishLocale),
		//external_intersection: getAttributeValue_fromModel_ByTypeNumber(2100453, selectedModel, EnglishLocale),
		//SLAProcess_duration: getAttributeValue_fromModel_ByTypeNumber(199389, selectedModel, EnglishLocale),

		process_code: getAttributeValueFromModelByGuid("1718f730-ed0b-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
		process_name: selectedModelName,
		process_description: getAttributeValueFromModelByGuid("e3886f40-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
		process_actors: selectedModel
			.ObjDefListBySymbols([Constants.ST_ORGUNIT_TYPE_LANE, Constants.ST_ORGUNIT_LANE, Constants.ST_POSITION_LANE, Constants.ST_ROLE_LANE])
			.map(function (obj) {
				return obj.Name(1033);
			})
			.join("/ "),
		process_trigger: selectedModel
			.ObjDefListBySymbols([Constants.ST_BPMN_TIMER_START_EVENT, Constants.ST_BPMN_TIMER_START_NI, Constants.ST_BPMN_TIMER_SE, Constants.ST_BPMN_START_EVENT, Constants.ST_BPMN_START_EV, Constants.ST_BPMN_PARALLEL_MULTIPLE_START, Constants.ST_BPMN_PARALLEL_MULTIPLE_START_NI, Constants.ST_BPMN_MSG_SE, Constants.ST_BPMN_MESSAGE_START_EVENT, Constants.ST_BPMN_MESSAGE_START_NI, Constants.ST_BPMN_ESCALATION_START, Constants.ST_BPMN_ESCALATION_START_NI])
			.map(function (obj) {
				return obj.Name(1033);
			})
			.join("\n"),
		//process_trigger: getAttributeValueFromModelByGuid("cce4b051-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
		//process_endpoint: getAttributeValueFromModelByGuid("bd7b6051-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
		process_endpoint: selectedModel
			.ObjDefListBySymbols([Constants.ST_BPMN_END_EVENT, Constants.ST_BPMN_END_EV, Constants.ST_BPMN_ESCALATION_END])
			.map(function (obj) {
				return obj.Name(1033);
			})
			.join("\n"),
		//system_used: getAttributeValueFromModelByGuid("1cba6bd1-f28b-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
		//getSystemsUsed_Model(selectedObjects)
		systems_used: (v => (v == null || v === 0 || String(v).trim() === "" || String(v).trim() === "-" ? "NA" : v))(getSystemsUsed_Model(selectedObjects)),
		internal_intersection: (v => (v == null || v === 0 || String(v).trim() === "" || String(v).trim() === "-" ? "NA" : v))(getAttributeValueFromModelByGuid("ab639220-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale)),
		external_intersection: (v => (v == null || v === 0 || String(v).trim() === "" || String(v).trim() === "-" ? "NA" : v))(getAttributeValueFromModelByGuid("98cafb31-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale)),
		//SLA_Process_duration:getSLA_Model(selectedObjects,selectedModel)------------------------------------SLA PROCESS DURATION (UNCOMMENT WHEN NEEDED)-----------------------------

		/*((v) =>
    v == null || v === 0 || v == "0000:00:00:00" || String(v).trim() === "0000:00:00:00" ? "NA" : v
  )(getSLA_Model(selectedObjects))*/
	};

	if (processCardAttrbuites.process_code == null || processCardAttrbuites.process_code == "-") {
		processCardAttrbuites.process_code = generateProcessID(selectedGroupPath);
	}

	//2d6e5de0-3bc1-11f0-6b91-005056a2de0e
	// Process card attributes
	// var processCardAttrbuites = {
	// process_code: getAttributeValueFromModelByGuid("1718f730-ed0b-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// process_name: getAttributeValueFromModelByGuid("06496071-ed0b-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// process_description: getAttributeValueFromModelByGuid("e3886f40-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// process_actors: getAttributeValueFromModelByGuid("-", selectedModel, EnglishLocale),
	// process_trigger: getAttributeValueFromModelByGuid("cce4b051-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// process_endpoint: getAttributeValueFromModelByGuid("bd7b6051-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// system_used: getAttributeValueFromModelByGuid("1cba6bd1-f28b-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// internal_intersection: getAttributeValueFromModelByGuid("ab639220-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// external_intersection: getAttributeValueFromModelByGuid("98cafb31-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	// salProcess_duration: getAttributeValueFromModelByGuid("79e71d20-ed0a-11ef-6b91-005056a2de0e", selectedModel, EnglishLocale),
	//
	// };

	// " Name             typeNumber                   GUID
	// "Process Name		         1641701	 06496071-ed0b-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "Process Code		         1707237	 1718f730-ed0b-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "Process description            1603	AT_PROC_DESC	ARIS default	\UML 1.4 Use case; 	"
	// "Process Trigger		      1510629	cce4b051-ed0a-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "Process End point		      1445093	bd7b6051-ed0a-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "System Used		         3017957	1cba6bd1-f28b-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "Internal Intersection		1379557	ab639220-ed0a-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "External Intersection		1314021	98cafb31-ed0a-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	// "SLA Process Duration		395997	79e71d20-ed0a-11ef-6b91-005056a2de0e	User-defined	\WALAA_BPMN; 	"
	//

	PAGE_NEW(oOutput, true);
	sectionPageSetup(oOutput, true);

	// Define a function to add rows dynamically
	function addTableRow(header, value) {
		oOutput.TableRow();
		oOutput.ResetFrameStyle();
		oOutput.TableCell("", 30, ID_DEFAULT_FONT, 10, RGB(white), RGB(blue), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		TextLine(header, table_body, "left", white);
		oOutput.TableCell("", 70, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		//TextLine(value, table_body, "left", black);
		TextLine(bulletedText(value), table_body, "left", black);
	}

	//title and description
	TextLine(processCardAttrbuites.process_name, page_title_font_size, "left-bold", blue, "main-title");
	emptyLine();

	TextLine("Process Card", sub_title_font_size, "left", lightblue, "sub-title");
	emptyLine();
	TextLine(tablesDescriptions.processCard, description_font_size, "left", black);
	emptyLine();

	// process card table;
	oOutput.BeginTable(100, RGB(black), RGB(white), Constants.FMT_LEFT | Constants.FMT_REPEAT_HEADER, 0);
	for (var [key, value] of Object.entries(processCardAttrbuites)) {
		var _key = key.split("_").join(" ");

		addTableRow(_key.charAt(0).toUpperCase() + _key.slice(1), value);
	}

	oOutput.EndTable("", 100, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_LEFT, 0);
	PAGE_END(oOutput);
}

function page3() {
	// process graphic
	PAGE_NEW(oOutput, true);
	// sectionPageSetup_Diagram(oOutput,selectedModelName + " Diagram" )
	sectionPageSetup(oOutput, true);
	TextLine(selectedModelName + " Diagram", sub_title_font_size, "left", lightblue, "sub-title");
	writeData1(oOutput, selectedModel.Graphic(true, false, EnglishLocale, true)); //.getPictureTiled ( 100, 100 ))
	PAGE_END(oOutput);
}

function page4() {
	// process Narrations
	PAGE_NEW(oOutput, true);
	sectionPageSetup(oOutput, true);

	// Filter and sort objects based on numeric suffix in the object definition name
	var result = selectedObjects
		.filter(function (item) {
			//var selectedAttr = item.Attribute(5442261,1033).getValue();
			var selectedAttr = item.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue();

			return selectedAttr != "" && /\d+$/.test(selectedAttr); // Keep only items ending with a number
		})
		.sort(function (a, b) {
			return parseInt(a.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10) - parseInt(b.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10);
		});

	// Define Table Headers
	var processNarrationsTableHeader = {
		stepNo: ["Step No.", 8],
		refCode: ["Ref Code", 8],
		descriptionActor: ["Description", 30],
		actor: ["Actor", 20],
		activityType: ["Activity Type", 10],
		document: ["Document", 11],
		comments: ["Comments", 13],
	};

	// page start

	//Title and Description
	TextLine("Process Narrations", sub_title_font_size, "left", lightblue, "sub-title");
	emptyLine();
	TextLine(tablesDescriptions.processNarrations, description_font_size, "left", black);
	emptyLine();

	// Process Narrations Table
	oOutput.BeginTable(100, RGB(black), RGB(white), Constants.FMT_LEFT | Constants.FMT_REPEAT_HEADER, 0);
	oOutput.TableRow();

	// Loop through headers dynamically
	Object.keys(processNarrationsTableHeader).forEach(function (key) {
		// oOutput.TableCell(processNarrationsTableHeader[key], cellWidth, fontName, fontSize, textColor, textFormat, 0, Constants.FMT_CENTER, 0);

		oOutput.TableCell("", processNarrationsTableHeader[key][1], ID_DEFAULT_FONT, table_font_size, RGB(white), RGB(blue), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		TextLine(processNarrationsTableHeader[key][0], table_font_size, "center", white);
	});

	function getProcessActor(obj) {
		var cnxList = obj.CxnListFilter(Constants.EDGES_INOUT, [65, 218]);
		return cnxList == ""
			? "-"
			: cnxList
					.map(function (value) {
						return value.SourceObjDef().Name(1033);
					})
					.join();
	}

	// Populate Table with Sorted Objects
	result.forEach(function (obj) {
		oOutput.TableRow();

		var rowData = {
			//stepNo: getAttributeValue_fromModel_ByTypeNumber(5442261, obj, 1033),
			//refCode: getAttributeValue_fromModel_ByTypeNumber(1838309, obj, 1033),
			//descriptionActor: getAttributeValue_fromModel_ByTypeNumber(527589, obj, 1033),
			stepNo: getAttributeValueFromModelByGuid("efc85340-f1c1-11ef-6b91-005056a2de0e", obj, EnglishLocale).match(/\d+/)[0],
			refCode: getAttributeValueFromModelByGuid("516b7f30-ed0a-11ef-6b91-005056a2de0e", obj, EnglishLocale),
			descriptionActor: getAttributeValueFromModelByGuid("e6366db0-ed09-11ef-6b91-005056a2de0e", obj, EnglishLocale),
			actor: getProcessActor(obj), // Assuming actor attribute ID
			activityType: getObjectSymbolName(obj, EnglishLocale),
			document: "-",
			comments: getAttributeValueFromModelByGuid("d5bdd091-ed09-11ef-6b91-005056a2de0e", obj, EnglishLocale),
		};

		//Description should be filled with Title if it's empty.
		if (rowData["descriptionActor"] == "-") {
			rowData["descriptionActor"] = obj.Name(EnglishLocale);
		}

		var stepSystem = obj.CxnListFilter(Constants.EDGES_INOUT, [221]).map(function (cnx) {
			return cnx.SourceObjDef().Name(1033);
		});

		//Adding System Used to the end of the description
		if (stepSystem != "") {
			rowData["descriptionActor"] += " ( " + stepSystem + " )";
		}

		// Remove (Collapsed) from collapsed Subprocesses
		if (rowData["activityType"] == "Subprocess (collapsed)") {
			rowData["activityType"] = "Subprocess";
		}

		Object.keys(processNarrationsTableHeader).forEach(function (key) {
			var cellFontColor = RGB(black);
			var textFontColor = black;
			// change color for start and end event
			if (key == "activityType") {
				if (rowData[key] == "End event") {
					var cellFontColor = RGB(red);
					var textFontColor = red;
				} else if (rowData[key] == "Start event") {
					var cellFontColor = RGB(green);
					var textFontColor = green;
				}
			}
			oOutput.TableCell("", processNarrationsTableHeader[key][1], ID_DEFAULT_FONT, 10, cellFontColor, RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
			if (key == "refCode" || key == "stepNo") {
				TextLine(rowData[key], table_font_size, "center", textFontColor);
			} else {
				TextLine(rowData[key], table_font_size, "left", textFontColor);
			}
		});

		// var stepNo = getAttributeValue_fromModel_ByTypeNumber(5442261, obj, 1033);
		// var refCode = getAttributeValue_fromModel_ByTypeNumber(1248485, obj, 1033);
		// var descriptionActor = getAttributeValue_fromModel_ByTypeNumber(1117413, obj, 1033);
		// var activityType = getObjectSymbolName(obj,1033)
		// var document = "-";

		// // Insert Table Cells
		//   oOutput.TableCell("", processNarrationsTableHeader.stepNo[1], ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		// TextLine(stepNo, content_Table_size, "left", black);

		//   oOutput.TableCell("", 10, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		// TextLine(refCode, content_Table_size, "left", black);

		//   oOutput.TableCell("", 30, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		// TextLine(descriptionActor, content_Table_size, "left", black);

		//   oOutput.TableCell("", 20, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		// TextLine(activityType, content_Table_size, "left", black);

		//   oOutput.TableCell("", 20, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		// TextLine(document, content_Table_size, "left", black);
	});

	oOutput.EndTable("", 100, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_LEFT, 0);
	PAGE_END(oOutput);
}

function page5() {
	// process Narrations
	PAGE_NEW(oOutput, true);
	sectionPageSetup(oOutput, true);

	// Filter and sort objects based on numeric suffix in the object definition name
	var result = selectedObjects
		.filter(function (item) {
			var stepNumber = item.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue();
			var itemType = item.Type();

			return stepNumber != "" && /\d+$/.test(stepNumber) && itemType != "" && itemType == "Function";
		})
		.sort(function (a, b) {
			return parseInt(a.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10) - parseInt(b.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10);
		});
	//  var RACI = { R : [65 ,  218 ]  , A : [232 ,323]  , S :  [324 , 233] , C : [355 , 358] , I :[326 , 266] }

	// var RACI = { R : [65 ,  218 ]  , A : [232 ,323]  , C : [355 , 358] , I :[326 , 266] };
	var raci = { 65: "R", 218: "R", 232: "Ac", 323: "Ac", 355: "C", 358: "C", 326: "I", 266: "I" };

	// Define Table Headers
	var raciMatrixHeader = {
		stepNo: "Step No.",
		refCode: "Responsible (R)",
		descriptionActor: "Accountable (A)",
		actor: "Consult (C)",
		activityType: "Inform (I)",
	};

	// page start

	//Title and Description
	TextLine("RACI Matrix", sub_title_font_size, "left", lightblue, "sub-title");
	emptyLine();
	TextLine(tablesDescriptions.raciMatrix, description_font_size, "left", black);
	emptyLine();

	// Process Narrations Table
	oOutput.BeginTable(100, RGB(black), RGB(white), Constants.FMT_LEFT | Constants.FMT_REPEAT_HEADER, 0);
	oOutput.TableRow();

	// Loop through headers dynamically
	Object.keys(raciMatrixHeader).forEach(function (key) {
		// oOutput.TableCell(processNarrationsTableHeader[key], cellWidth, fontName, fontSize, textColor, textFormat, 0, Constants.FMT_CENTER, 0);
		if (key == "stepNo") {
			oOutput.TableCell("", 8, ID_DEFAULT_FONT, table_font_size, RGB(white), RGB(blue), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		} else {
			oOutput.TableCell("", 23, ID_DEFAULT_FONT, table_font_size, RGB(white), RGB(blue), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		}
		TextLine(raciMatrixHeader[key], table_font_size, "center", white);
	});

	//var raciTypeNumber = Object.keys(raci).flat();
	var raciTypeNumber = Object.keys(raci);
	var width = Math.floor(raciTypeNumber / 100);
	// Populate Table with Sorted Objects
	result.forEach(function (obj) {
		var rowData = {
			stepNo: obj.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue(),
			R: [],
			Ac: [],
			C: [],
			I: [],
		};

		obj.CxnListFilter(Constants.EDGES_INOUT, raciTypeNumber).forEach(function (value) {
			var raciLetter = raci[value.TypeNum()];
			if (rowData[raciLetter] != null && rowData[raciLetter] != undefined) {
				rowData[raciLetter].push(value.SourceObjDef().Name(1033));
			}
		});
		oOutput.TableRow();

		Object.keys(rowData).forEach(function (key) {
			if (key == "stepNo") {
				oOutput.TableCell("", 8, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
				TextLine(rowData[key].match(/\d+/)[0], table_font_size, "center", black);
			} else {
				oOutput.TableCell("", 23, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
				TextLine(
					rowData[key] !== "" && String(rowData[key]).trim() !== "" && rowData[key] != null // Check for non-empty and not null/undefined
						? Array.isArray(rowData[key])
							? rowData[key].join("/")
							: rowData[key]
						: "-",
					table_font_size,
					"left",
					black
				);
			}
		});
	});

	oOutput.EndTable("", 100, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_LEFT, 0);
	PAGE_END(oOutput);
}

// SLA Table
function page6() {
	// First filter and sort the objects to check if we have any valid rows
	var result = selectedObjects
		.filter(function (item) {
			var selectedAttr = item.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue();
			return selectedAttr != "" && /\d+$/.test(selectedAttr); // Keep only items ending with a number
		})
		.sort(function (a, b) {
			return parseInt(a.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10) - parseInt(b.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10);
		});

	// Check if we have any rows with Time attribute before proceeding
	var hasValidRows = false;
	var slaTypeNumber = Object.keys({
		"36807610-18ae-11f0-5339-005056a03ba8": "D",
		"47640a01-18ae-11f0-5339-005056a03ba8": "S",
		"43a3d840-18af-11f0-5339-005056a03ba8": "R",
		"2d6e5de0-3bc1-11f0-6b91-005056a2de0e": "T",
	});

	for (var i = 0; i < result.length; i++) {
		var obj = result[i];
		var timeAttr = obj.Attribute(Filter.UserDefinedAttributeTypeNum(slaTypeNumber[3]), 1033).getValue();
		if (timeAttr != "" && timeAttr != null && timeAttr != undefined) {
			hasValidRows = true;
			break;
		}
	}

	var showDummyRow = !hasValidRows;

	// Always proceed with page creation
	PAGE_NEW(oOutput, true);
	sectionPageSetup(oOutput, true);

	// Define Table Headers
	var slaHeader = {
		stepNo: "Step No.",
		Description_sla: "Description",
		Receiver: "Receiver",
		Sender: "Sender",
		Time: "Time",
	};

	//Title and Description
	TextLine("SLA Time", sub_title_font_size, "left", lightblue, "sub-title");
	emptyLine();
	TextLine(tablesDescriptions.slaTime, description_font_size, "left", black);
	emptyLine();

	// Process SLA Table
	oOutput.BeginTable(100, RGB(black), RGB(white), Constants.FMT_LEFT | Constants.FMT_REPEAT_HEADER, 0);
	oOutput.TableRow();

	// Table headers
	Object.keys(slaHeader).forEach(function (key) {
		if (key == "stepNo") {
			oOutput.TableCell("", 8, ID_DEFAULT_FONT, table_font_size, RGB(white), RGB(blue), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		} else {
			oOutput.TableCell("", 23, ID_DEFAULT_FONT, table_font_size, RGB(white), RGB(blue), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
		}
		TextLine(slaHeader[key], table_font_size, "center", white);
	});

	if (showDummyRow) {
		// Show a single dummy row with "-"
		oOutput.TableRow();
		Object.keys(slaHeader).forEach(function (key) {
			var width = key === "stepNo" ? 8 : 23;
			oOutput.TableCell("", width, ID_DEFAULT_FONT, table_font_size, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);
			TextLine("-", table_font_size, "center", black);
		});
	} else {
		// Populate table with valid data
		result.forEach(function (obj) {
			var rowData = {
				stepNo: obj.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue(),
				Description: obj.Attribute(Filter.UserDefinedAttributeTypeNum(slaTypeNumber[0]), 1033).getValue(),
				Sender: obj.Attribute(Filter.UserDefinedAttributeTypeNum(slaTypeNumber[1]), 1033).getValue(),
				Receiver: obj.Attribute(Filter.UserDefinedAttributeTypeNum(slaTypeNumber[2]), 1033).getValue(),
				Time: obj.Attribute(Filter.UserDefinedAttributeTypeNum(slaTypeNumber[3]), 1033).getValue(),
			};

			if (rowData["Time"] != "" && rowData["Time"] != null && rowData["Time"] != undefined) {
				oOutput.TableRow();

				Object.keys(rowData).forEach(function (key) {
					var width = key === "stepNo" ? 8 : 23;
					oOutput.TableCell("", width, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_CENTER | Constants.FMT_VCENTER, 0);

					var value = rowData[key];
					var outputValue = value !== "" && value != null && String(value).trim() !== "" ? (Array.isArray(value) ? value.join("/") : value) : "-";

					if (key === "stepNo") {
						TextLine(value.match(/\d+/)[0], table_font_size, "center", black);
					} else {
						TextLine(outputValue, table_font_size, "left", black);
					}
				});
			}
		});
	}

	oOutput.EndTable("", 100, ID_DEFAULT_FONT, 10, RGB(black), RGB(white), 0, Constants.FMT_LEFT, 0);
	PAGE_END(oOutput);
}

/*-------------------------------
   Helper Function Definitions
-------------------------------*/

// Starts a new page (optionally in landscape)
function PAGE_NEW(oOutput) {
	var landscape = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
	if (landscape) {
		oOutput.setPageWidth(297);
		oOutput.setPageHeight(210);
	} else {
		oOutput.setPageWidth(210);
		oOutput.setPageHeight(297);
	}
	oOutput.BeginSection(false, Constants.SECTION_INDEX);
}

// Ends the current page
function PAGE_END(oOutput) {
	ArisData.getActiveDatabase().clearCaches();
	oOutput.EndSection();
}

// Outputs an empty line; adjust the parameters as needed
function emptyLine(outputObj) {
	oOutput.OutputLn("  ", ID_DEFAULT_FONT, 12, RGB(0), Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);
}
function emptyLineTable(outputObj) {
	oOutput.OutputLn("  ", ID_DEFAULT_FONT, 5, RGB(0), Constants.C_TRANSPARENT, Constants.FMT_LEFT, 0);
}

// Finalizes and writes the report
function runReport(outputObj) {
	outputObj.WriteReport();
}
function RGB(color) {
	var r = color[0];
	var g = color[1];
	var b = color[2];
	return new java.awt.Color(r / 255.0, g / 255.0, b / 255.0, 1).getRGB() & 0xffffff;
}

function TextLine(text, size, algin, Color, TOC_LEVEL, highlightColor, no_newLine) {
	var FMT;
	var size = !size ? 10 : size;
	var algin = !algin ? "left" : algin;
	var Color = !Color ? [0, 0, 0] : Color;
	var highlightColor = !highlightColor ? Constants.C_TRANSPARENT : highlightColor;
	var TOC_LEVEL = !TOC_LEVEL ? null : TOC_LEVEL;
	var checkFMT = algin.split("-");
	for (var i = 0; i < checkFMT.length; i++) {
		var element = checkFMT[i];
		if (element == "center") {
			FMT = Constants.FMT_CENTER;
		}
		if (element == "left") {
			FMT = Constants.FMT_LEFT;
		}
		if (element == "right") {
			FMT = Constants.FMT_RIGHT;
		}
		if (element == "justify") {
			FMT = Constants.FMT_JUSTIFY;
		}
		if (element == "bold") {
			FMT = FMT | Constants.FMT_BOLD;
		}
		if (element == "italic") {
			FMT = FMT | Constants.FMT_ITALIC;
		}
	}
	if (TOC_LEVEL != null) {
		switch (TOC_LEVEL) {
			case "main-title":
				FMT = FMT | Constants.FMT_TOCENTRY0;
				break;
			case "sub-title":
				FMT = FMT | Constants.FMT_TOCENTRY1;
				break;
			case "sub-sub-title":
				FMT = FMT | Constants.FMT_TOCENTRY2;
				break;
			case "sub-sub-sub-title":
				FMT = FMT | Constants.FMT_TOCENTRY3;
				break;
		}
	}

	if (no_newLine === true) {
		oOutput.Output(text, ID_DEFAULT_FONT, size, RGB(Color), highlightColor, FMT, 0.71);
	} else {
		oOutput.OutputLn(text, ID_DEFAULT_FONT, size, RGB(Color), highlightColor, FMT, 0.71);
	}
}

/*
    1-Setup Table of content Levels 
    2-Header With ImgHeader
    3-Fotter [ confdantial , PageNumber ]
*/

function sectionPageSetup(outputObj) {
	var showFooter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
	outputObj.SetAutoTOCNumbering(true);
	outputObj.SetTOCFormat(0, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_WHITE, Constants.FMT_LEFT | Constants.FMT_BOLD, 0, 0, 0, 0);
	outputObj.SetTOCFormat(1, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_WHITE, Constants.FMT_LEFT, 5, 5, 2, 2);
	outputObj.SetTOCFormat(2, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_WHITE, Constants.FMT_LEFT | Constants.FMT_ITALIC, 10, 5, 2, 2);
	outputObj.SetTOCFormat(3, ID_DEFAULT_FONT, 9.5, Constants.C_BLACK, Constants.C_WHITE, Constants.FMT_LEFT, 15, 5, 2, 2);

	createHeader(outputObj);
	if (showFooter) {
		createFooter(outputObj);
	}
	function createHeader(outputObj) {
		outputObj.BeginHeader();
		oOutput.OutGraphicAbsolute(headerImg, 0.0, 0.0, 278, 27, false);
		outputObj.EndHeader();
	}
	oOutput.setLeftMargin(20);
	oOutput.setRightMargin(20);
	function createFooter(outputObj) {
		outputObj.BeginFooter();
		outputObj.BeginTable(100, Constants.C_TRANSPARENT, Constants.C_TRANSPARENT, Constants.FMT_CENTER | Constants.FMT_NOBORDER, 0);
		outputObj.TableRow();
		outputObj.TableCell("", 50, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_VTOP | Constants.FMT_LEFT, 0);
		TextLine(selectedModelName, content_footer, "left", lightGray);
		outputObj.TableRow();
		outputObj.ResetFrameStyle();
		//confidential
		outputObj.TableCell("", 50, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_VTOP | Constants.FMT_LEFT, 0);

		TextLine("Internal", content_footer, "left", d_org);

		//Page Number
		outputObj.TableCell("", 50, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_VTOP | Constants.FMT_RIGHT, 0);
		outputObj.OutputField(Constants.FIELD_PAGE, ID_DEFAULT_FONT, 10, RGB(0, 0, 0), Constants.C_TRANSPARENT, Constants.FMT_RIGHT);
		TextLine("Page", content_footer, "right", lightGray);

		oOutput.EndTable("", 100, ID_DEFAULT_FONT, 10, Constants.C_BLACK, Constants.C_TRANSPARENT, 0, Constants.FMT_LEFT, 0);
		outputObj.EndFooter();
	}
}

//Library imports
mode$att$g = 0;

function getAttributeValueFromModelByGuid(guid, modelObj, currentLocale) {
	var _modelObj$Attribute$g, _ArisData, _value$split;
	var defaultValue = "-";
	var value = modelObj === null || modelObj === void 0 || (_modelObj$Attribute$g = modelObj.Attribute((_ArisData = ArisData) === null || _ArisData === void 0 || (_ArisData = _ArisData.ActiveFilter()) === null || _ArisData === void 0 ? void 0 : _ArisData.UserDefinedAttributeTypeNum(guid), currentLocale).getValue()) === null || _modelObj$Attribute$g === void 0 ? void 0 : _modelObj$Attribute$g.toString();
	var rawValue = value === null || value === void 0 || (_value$split = value.split(" ")) === null || _value$split === void 0 ? void 0 : _value$split.join("");
	if (!value || value === undefined || value === null || value == "" || rawValue.length < 1) {
		return defaultValue;
	} else {
		return replaceAsterisk(value);
	}
}

function getAttributeValue_fromModel_ByTypeNumber(TypeNumber, modelObj, currentLocale) {
	var _modelObj$Attribute, _value$split2;
	var defaultValue = "-";
	try {
		var value = modelObj === null || modelObj === void 0 || (_modelObj$Attribute = modelObj.Attribute(TypeNumber, currentLocale)) === null || _modelObj$Attribute === void 0 || (_modelObj$Attribute = _modelObj$Attribute.getValue()) === null || _modelObj$Attribute === void 0 ? void 0 : _modelObj$Attribute.toString();
		var rawValue = value === null || value === void 0 || (_value$split2 = value.split(" ")) === null || _value$split2 === void 0 ? void 0 : _value$split2.join("");
		if (!value || value === undefined || value === null || value == "" || rawValue.length <= 2) {
			return defaultValue;
		} else {
			return replaceAsterisk(value);
		}
	} catch (e) {
		return defaultValue;
	}
}

function getObjectSymbolName(modelObject, currentLocale) {
	var defaultValue = "-";
	try {
		if (!modelObject) {
			return defaultValue;
		}

		var symbolName = ArisData.getConfiguration().getSymbolItem(modelObject.getDefaultSymbolNum()).getOriginalName(currentLocale);

		if (symbolName !== "" && symbolName !== null && symbolName !== undefined) {
			return symbolName;
		} else {
			return defaultValue;
		}
	} catch (error) {
		return defaultValue;
	}
}

function writeData1(p_output, p_IModelPicture) {
	p_output.BeginParagraph(Constants.FMT_CENTER, 5, 5, 5, 5, 0);
	p_output.OutGraphic(p_IModelPicture, -1, p_output.getCurrentMaxWidth(), p_output.GetPageHeight() - p_output.GetTopMargin() - p_output.GetBottomMargin() - 15);
	p_output.EndParagraph();
}

function replaceAsterisk(value) {
	var lines = value.split("\n"); // Split the text into lines
	for (var i = 0; i < lines.length; i++) {
		var line = lines[i];
		// Check if the line starts with spaces/tabs followed by *
		if (/^[ \t]*\*/.test(line)) {
			// Replace only the first * with •
			lines[i] = line.replace("*", "•");
		}
	}
	return lines.join("\n"); // Rejoin the lines
}

function getSecondToLastSection(str) {
	const parts = str.split("\\\\");
	if (parts.length >= 2) {
		return parts[parts.length - 2].split("_")[1];
	}
	return "-"; // or some fallback value
}

function generateProcessID(path) {
	// Split the path by backslash '\'
	let parts = path
		.split("\\\\")
		.map(part => part.trim())
		.filter(part => part !== "");

	// Get only the last 4 parts
	if (parts.length < 4) {
		throw new Error("Path must have at least 4 parts relevant to the ID generation.");
	}
	let lastFourParts = parts.slice(-4);

	// Extract part before "_" and make it uppercase
	let idParts = lastFourParts.map(part => {
		let beforeUnderscore = part.split("_")[0];
		return beforeUnderscore.toUpperCase();
	});

	// Join them with backslash '\'
	return idParts.join("\\");
}

// Return all systems used across model steps/tasks.
function getSystemsUsed_Model(selectedObjects) {
	// Filter and sort objects based on numeric suffix in the object definition name
	var result = selectedObjects
		.filter(function (item) {
			var selectedAttr = item.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue();

			return selectedAttr != "" && /\d+$/.test(selectedAttr); // Keep only items ending with a number
		})
		.sort(function (a, b) {
			return parseInt(a.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10) - parseInt(b.Attribute(Filter.UserDefinedAttributeTypeNum("efc85340-f1c1-11ef-6b91-005056a2de0e"), 1033).getValue().match(/\d+$/)[0], 10);
		});

	var mySystems = new Set();

	result.forEach(function (obj) {
		var cnxList = obj.CxnListFilter(Constants.EDGES_INOUT, [221]);
		var systemNames = cnxList.map(function (cnx) {
			return cnx.SourceObjDef().Name(1033);
		});

		// Add each system name individually to the Set
		systemNames.forEach(function (name) {
			name = name.trim();
			if (name !== "") {
				mySystems.add(name);
			}
		});
	});

	var SystemsUsed = Array.from(mySystems).join(" / ");
	return SystemsUsed;
}

// Return SLA Duration across model steps/tasks.
function getSLA_Model(selectedObjects, selectedModel) {
	var modelValue = selectedModel.Attribute(Filter.UserDefinedAttributeTypeNum("79e71d20-ed0a-11ef-6b91-005056a2de0e"), 1033).getValue();

	if (!(v => v == null || v === 0 || v == "0000:00:00:00" || String(v).trim() === "0000:00:00:00")(modelValue)) {
		return String(modelValue);
	}

	var totalSeconds = 0;
	var objects = selectedObjects;

	for (var i = 0; i < objects.length; i++) {
		var obj = objects[i];
		var rawValue = obj.Attribute(Filter.UserDefinedAttributeTypeNum("2d6e5de0-3bc1-11f0-6b91-005056a2de0e"), 1033).getValue();

		if (!rawValue) continue;
		rawValue = String(rawValue).trim();

		var parts = rawValue.split(":");
		if (parts.length !== 4) continue;

		var days = +parts[0],
			hours = +parts[1],
			minutes = +parts[2],
			seconds = +parts[3];

		totalSeconds += days * 86400 + hours * 3600 + minutes * 60 + seconds;
	}

	var d = Math.floor(totalSeconds / 86400);
	var h = Math.floor((totalSeconds % 86400) / 3600);
	var m = Math.floor((totalSeconds % 3600) / 60);
	var s = totalSeconds % 60;

	return String(d).padStart(4, "0") + ":" + String(h).padStart(2, "0") + ":" + String(m).padStart(2, "0") + ":" + String(s).padStart(2, "0");
}

function bulletedText(value) {
	var result = value
		.split(/\r?\n/)
		.map(line => `•   ${line}`)
		.join("\n");
	return result;
}

main();
